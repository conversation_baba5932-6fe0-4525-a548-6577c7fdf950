---
import { But<PERSON> } from "@/components/ui/button";
---

<section class="relative overflow-hidden min-h-[90vh] flex items-center py-12 md:py-16 overflow-x-hidden">
  <!-- Simplified Background elements -->
  <div class="absolute inset-0 -z-10">
    <div class="absolute inset-0 bg-background dark:bg-zinc-950"></div>
    <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent opacity-30 dark:opacity-20"></div>
    <div class="absolute inset-0 bg-noise-pattern opacity-[0.07] mix-blend-soft-light"></div>
    <!-- Optional: very subtle, slow blobs -->
    <div class="absolute top-1/4 left-1/4 w-[500px] h-[500px] rounded-full radial-gradient-primary opacity-05 blur-[80px] animate-blob-slowest"></div>
    <div class="absolute bottom-1/4 right-1/4 w-[400px] h-[400px] rounded-full radial-gradient-secondary opacity-03 blur-[70px] animate-blob-slowest animation-delay-4000"></div>
  </div>

  <div class="container relative z-10">
    <div class="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12 items-center">
      <!-- Left column: Content -->
      <div class="lg:col-span-5 space-y-8 text-center lg:text-left">
        <div class="flex items-center gap-3 slide-in-left opacity-0 justify-center lg:justify-start" style="--slide-delay: 0.1s;">
          <div class="h-px w-12 bg-primary"></div>
          <span class="text-sm uppercase tracking-widest text-primary font-medium">Academic Portal</span>
        </div>

        <div class="space-y-3">
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight split-text-reveal opacity-0" style="--split-delay: 0.2s;">
            <span class="block">DCCP<span class="text-primary">Hub</span></span>
          </h1>
          <p class="text-lg md:text-xl text-muted-foreground font-light fade-in-up opacity-0" style="--fade-delay: 0.5s;">
            The next generation learning platform for students and educators.
          </p>

          <!-- Mobile App Announcement -->
          <div class="flex flex-col sm:flex-row gap-3 pt-4 fade-in-up opacity-0" style="--fade-delay: 0.7s;">
            <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-green-500/10 border border-green-500/20 text-green-600 dark:text-green-400">
              <div class="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
              <span class="text-sm font-medium">Android Beta Available</span>
            </div>
            <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 text-blue-600 dark:text-blue-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <span class="text-sm font-medium">iOS Coming Soon</span>
            </div>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-4 pt-6 fade-in-up opacity-0 justify-center lg:justify-start" style="--fade-delay: 0.9s;">
          <Button size="lg" className="relative group overflow-hidden bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary transition-all duration-500 shadow-lg hover:shadow-primary/40">
            <span class="relative z-10 flex items-center gap-2">
              <span>Access Portal</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="transition-transform duration-300 group-hover:translate-x-1"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
            </span>
          </Button>
          <Button variant="outline" size="lg" className="group border-green-500/30 hover:border-green-500/50 hover:bg-green-500/5 transition-colors duration-300 shadow-sm hover:shadow-green-500/20">
            <a href="https://portal.dccp.edu.ph/apk/download/DCCPHub_latest.apk" class="flex items-center gap-2 text-foreground" download>
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor" class="text-green-500">
                <path d="M17.523 15.3414c-.5665 0-1.0253-.4588-1.0253-1.0253s.4588-1.0253 1.0253-1.0253 1.0253.4588 1.0253 1.0253-.4588 1.0253-1.0253 1.0253zm-11.046 0c-.5665 0-1.0253-.4588-1.0253-1.0253s.4588-1.0253 1.0253-1.0253 1.0253.4588 1.0253 1.0253-.4588 1.0253-1.0253 1.0253zm11.405-6.02l1.14-2.02c.08-.14.03-.32-.11-.4-.14-.08-.32-.03-.4.11l-1.15 2.04c-.93-.41-1.96-.64-3.06-.64s-2.13.23-3.06.64L9.13 6.97c-.08-.14-.26-.19-.4-.11-.14.08-.19.26-.11.4l1.14 2.02C6.59 10.85 4.5 13.6 4.5 16.5h15c0-2.9-2.09-5.65-5.27-7.22z"/>
              </svg>
              <span>Download Android</span>
            </a>
          </Button>
        </div>

        <!-- Mobile App Quick Info -->
        <div class="pt-4 fade-in-up opacity-0" style="--fade-delay: 1.1s;">
          <div class="flex flex-col sm:flex-row gap-4 text-sm text-muted-foreground">
            <div class="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
              <span>Android 6.0+ supported</span>
            </div>
            <div class="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
              </svg>
              <span>Secure & verified APK</span>
            </div>
            <div class="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <span>iOS version in development</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Right column: Focused Image Showcase -->
      <div class="lg:col-span-7 flex items-center justify-center fade-in-right opacity-0" style="--fade-delay: 0.4s;">
        <div class="image-showcase-container relative w-full group mr-[-1rem] sm:mr-[-1.5rem] lg:mr-[-2rem] xl:mr-[-2.5rem]">
          <img
            id="portal-hero-img"
            src="/portal2light.png"
            alt="DCCP Hub Dashboard Showcase"
            class="w-full h-auto object-contain display-block transition-transform duration-300 ease-out"
          />
        </div>
      </div>
    </div>

    <!-- Interactive stats bar -->
    <div class="mt-16 lg:mt-24 fade-in-up opacity-0" style="--fade-delay: 1.1s;">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
        <div class="stat-card group p-4 md:p-6 rounded-xl border border-border/40 bg-card/40 backdrop-blur-sm hover:border-primary/30 hover:bg-card/60 transition-all duration-300 text-center">
          <div class="flex flex-col items-center">
            <div class="h-10 w-10 md:h-12 md:w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-3 group-hover:scale-110 transition-transform duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>
            </div>
            <span class="text-2xl md:text-3xl font-bold text-primary count-up">100</span>
            <span class="text-xs md:text-sm text-muted-foreground mt-1">% Online Access</span>
          </div>
        </div>
        <div class="stat-card group p-4 md:p-6 rounded-xl border border-border/40 bg-card/40 backdrop-blur-sm hover:border-primary/30 hover:bg-card/60 transition-all duration-300 text-center">
          <div class="flex flex-col items-center">
            <div class="h-10 w-10 md:h-12 md:w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-3 group-hover:scale-110 transition-transform duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>
            </div>
            <div class="flex items-baseline justify-center">
              <span class="text-2xl md:text-3xl font-bold text-primary count-up">24</span>
              <span class="text-xl md:text-2xl font-bold text-primary">/7</span>
            </div>
            <span class="text-xs md:text-sm text-muted-foreground mt-1">Availability</span>
          </div>
        </div>
        <div class="stat-card group p-4 md:p-6 rounded-xl border border-border/40 bg-card/40 backdrop-blur-sm hover:border-green-500/30 hover:bg-card/60 transition-all duration-300 text-center">
          <div class="flex flex-col items-center">
            <div class="h-10 w-10 md:h-12 md:w-12 rounded-full bg-green-500/10 flex items-center justify-center text-green-500 mb-3 group-hover:scale-110 transition-transform duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.523 15.3414c-.5665 0-1.0253-.4588-1.0253-1.0253s.4588-1.0253 1.0253-1.0253 1.0253.4588 1.0253 1.0253-.4588 1.0253-1.0253 1.0253zm-11.046 0c-.5665 0-1.0253-.4588-1.0253-1.0253s.4588-1.0253 1.0253-1.0253 1.0253.4588 1.0253 1.0253-.4588 1.0253-1.0253 1.0253zm11.405-6.02l1.14-2.02c.08-.14.03-.32-.11-.4-.14-.08-.32-.03-.4.11l-1.15 2.04c-.93-.41-1.96-.64-3.06-.64s-2.13.23-3.06.64L9.13 6.97c-.08-.14-.26-.19-.4-.11-.14.08-.19.26-.11.4l1.14 2.02C6.59 10.85 4.5 13.6 4.5 16.5h15c0-2.9-2.09-5.65-5.27-7.22z"/>
              </svg>
            </div>
            <span class="text-2xl md:text-3xl font-bold text-green-500 count-up">500</span>
            <span class="text-xs md:text-sm text-muted-foreground mt-1">Beta Downloads</span>
          </div>
        </div>
        <div class="stat-card group p-4 md:p-6 rounded-xl border border-border/40 bg-card/40 backdrop-blur-sm hover:border-primary/30 hover:bg-card/60 transition-all duration-300 text-center">
          <div class="flex flex-col items-center">
            <div class="h-10 w-10 md:h-12 md:w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-3 group-hover:scale-110 transition-transform duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"/></svg>
            </div>
            <span class="text-2xl md:text-3xl font-bold text-primary count-up">95</span>
            <span class="text-xs md:text-sm text-muted-foreground mt-1">% Satisfaction Rate</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Mobile App Banner -->
    <div id="mobile-app-banner" class="fixed bottom-6 right-6 z-50 opacity-0 translate-y-full transition-all duration-500 ease-out">
      <div class="bg-card border border-border/50 rounded-xl p-4 shadow-lg backdrop-blur-sm max-w-sm">
        <div class="flex items-start gap-3">
          <div class="h-12 w-12 rounded-lg bg-green-500/10 flex items-center justify-center shrink-0">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="text-green-500">
              <path d="M17.523 15.3414c-.5665 0-1.0253-.4588-1.0253-1.0253s.4588-1.0253 1.0253-1.0253 1.0253.4588 1.0253 1.0253-.4588 1.0253-1.0253 1.0253zm-11.046 0c-.5665 0-1.0253-.4588-1.0253-1.0253s.4588-1.0253 1.0253-1.0253 1.0253.4588 1.0253 1.0253-.4588 1.0253-1.0253 1.0253zm11.405-6.02l1.14-2.02c.08-.14.03-.32-.11-.4-.14-.08-.32-.03-.4.11l-1.15 2.04c-.93-.41-1.96-.64-3.06-.64s-2.13.23-3.06.64L9.13 6.97c-.08-.14-.26-.19-.4-.11-.14.08-.19.26-.11.4l1.14 2.02C6.59 10.85 4.5 13.6 4.5 16.5h15c0-2.9-2.09-5.65-5.27-7.22z"/>
            </svg>
          </div>
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-1">
              <h4 class="text-sm font-semibold">DCCPHub Mobile</h4>
              <span class="inline-flex items-center gap-1 px-2 py-0.5 rounded-full bg-green-500/10 text-green-600 dark:text-green-400 text-xs font-medium">
                <div class="w-1.5 h-1.5 rounded-full bg-green-500 animate-pulse"></div>
                Beta
              </span>
            </div>
            <p class="text-xs text-muted-foreground mb-3">Download the Android app for on-the-go access</p>
            <div class="flex gap-2">
              <a
                href="https://portal.dccp.edu.ph/apk/download/DCCPHub_latest.apk"
                class="inline-flex items-center gap-1 px-3 py-1.5 rounded-lg bg-green-500 text-white text-xs font-medium hover:bg-green-600 transition-colors"
                download
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" x2="12" y1="15" y2="3"></line>
                </svg>
                Download
              </a>
              <button id="close-banner" class="px-3 py-1.5 rounded-lg border border-border/50 text-xs font-medium hover:bg-muted transition-colors">
                Later
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .bg-noise-pattern {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.95' numOctaves='1' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  }
  .radial-gradient-primary {
    background: radial-gradient(circle at center, hsl(var(--primary)) 0%, transparent 80%);
  }
  .radial-gradient-secondary {
    background: radial-gradient(circle at center, hsl(var(--secondary)) 0%, transparent 80%);
  }

  .image-showcase-container img {
    display: block; /* Ensures no extra space below image */
    max-width: 100%; /* Ensures image is responsive and doesn't overflow container */
  }

  @keyframes fade-in-up {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  @keyframes fade-in-right {
    0% {
      opacity: 0;
      transform: translateX(30px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  @keyframes slide-in-left {
    0% {
      opacity: 0;
      transform: translateX(-30px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  @keyframes split-text-reveal {
    0% {
      opacity: 0;
      transform: translateY(40px);
      filter: blur(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
      filter: blur(0);
    }
  }
  
  /* Float animation removed as accents are removed for now */
  /* @keyframes float { ... } */
  /* .animate-float { ... } */

  @keyframes blob-slowest {
    0%, 100% {
      border-radius: 50% 50% 50% 50% / 50% 50% 50% 50%;
      transform: translate(0,0) rotate(0deg) scale(1);
    }
    25% {
      border-radius: 55% 45% 60% 40% / 40% 60% 45% 55%;
      transform: translate(10px, -5px) rotate(5deg) scale(1.02);
    }
    50% {
       border-radius: 60% 40% 70% 30% / 30% 70% 40% 60%;
      transform: translate(-10px, 10px) rotate(-5deg) scale(1);
    }
    75% {
      border-radius: 45% 55% 40% 60% / 60% 40% 55% 45%;
      transform: translate(5px, 5px) rotate(2deg) scale(1.01);
    }
  }
  .animate-blob-slowest {
    animation: blob-slowest 25s linear infinite;
  }
  .animation-delay-4000 {
    animation-delay: 4s; /* For staggered blob animations */
  }

  .fade-in-up { animation: fade-in-up 0.8s ease-out forwards; animation-delay: var(--fade-delay, 0s); }
  .fade-in-right { animation: fade-in-right 0.8s ease-out 0.2s forwards; animation-delay: var(--fade-delay, 0s); }
  /* .accent-icon.fade-in-right { animation-delay: calc(var(--fade-delay, 0s) + var(--float-delay, 0s) + 0.3s); } */ /* Accent icon animation rule removed */
  .slide-in-left { animation: slide-in-left 0.8s ease-out forwards; animation-delay: var(--slide-delay, 0s); }
  .split-text-reveal { animation: split-text-reveal 1s cubic-bezier(0.16, 1, 0.3, 1) forwards; animation-delay: var(--split-delay, 0s); }

  .stat-card:hover .count-up { color: hsl(var(--primary)); transition: color 0.3s ease; }
  .stat-card { transition: transform 0.3s ease, box-shadow 0.3s ease; }
  .stat-card:hover { transform: translateY(-4px); box-shadow: 0 8px 20px -4px rgba(0,0,0,0.08); }
  :global(.dark) .stat-card:hover { box-shadow: 0 8px 20px -4px hsla(var(--primary), 0.15); }

  /* Mobile App Banner Styles */
  #mobile-app-banner {
    max-width: calc(100vw - 3rem);
  }

  @media (max-width: 640px) {
    #mobile-app-banner {
      bottom: 1rem;
      right: 1rem;
      left: 1rem;
      max-width: none;
    }
  }

  /* Enhanced mobile responsiveness */
  @media (max-width: 768px) {
    .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .grid.grid-cols-2.md\\:grid-cols-4 {
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }

    .stat-card {
      padding: 1rem;
    }

    .text-5xl.md\\:text-6xl.lg\\:text-7xl {
      font-size: 2.5rem;
      line-height: 1.1;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const countElements = document.querySelectorAll('.count-up');
    countElements.forEach(el => {
      const htmlEl = el as HTMLElement;
      const targetText = htmlEl.textContent;
      if (targetText) {
        const target = parseInt(targetText, 10);
        if (!isNaN(target)) {
          let count = 0;
          const duration = 2000;
          const frameDuration = 1000 / 60;
          const totalFrames = Math.round(duration / frameDuration);
          const increment = target / totalFrames;
          const counter = setInterval(() => {
            count += increment;
            if (count >= target) {
              htmlEl.textContent = target.toString();
              clearInterval(counter);
            } else {
              htmlEl.textContent = Math.floor(count).toString();
            }
          }, frameDuration);
        }
      }
    });

    // Intersection observer for accent icons removed as accents are currently removed.
    // const accentIcons = document.querySelectorAll('.accent-icon.opacity-0');
    // const observer = new IntersectionObserver((entries) => {
    //   entries.forEach(entry => {
    //     if (entry.isIntersecting) {
    //       entry.target.classList.remove('opacity-0'); 
    //     }
    //   });
    // }, { threshold: 0.5 }); 

    // accentIcons.forEach(icon => {
    //   observer.observe(icon);
    // });
  });

  // Helper to get theme from html class or localStorage (shadcn/ui compatible)
  function getCurrentTheme() {
    if (document.documentElement.classList.contains('dark')) return 'dark';
    if (localStorage.getItem('theme') === 'dark') return 'dark';
    return 'light';
  }

  function updateHeroImage() {
    const img = document.getElementById('portal-hero-img') as HTMLImageElement | null;
    if (!img) return;
    if (getCurrentTheme() === 'dark') {
      img.src = '/portal1.png';
    } else {
      img.src = '/portal2light.png';
    }
  }

  // Initial set
  updateHeroImage();

  // Listen for theme changes (shadcn/ui toggles the 'dark' class on <html>)
  const observer = new MutationObserver(updateHeroImage);
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });

  // Optionally, listen for storage changes (if theme is changed in another tab)
  window.addEventListener('storage', updateHeroImage);

  // Mobile App Banner functionality
  const mobileAppBanner = document.getElementById('mobile-app-banner');
  const closeBannerBtn = document.getElementById('close-banner');

  // Check if banner was previously dismissed
  const bannerDismissed = localStorage.getItem('dccp-mobile-banner-dismissed');

  // Show banner after 3 seconds if not dismissed
  if (!bannerDismissed && mobileAppBanner) {
    setTimeout(() => {
      mobileAppBanner.classList.remove('opacity-0', 'translate-y-full');
    }, 3000);
  }

  // Handle banner close
  if (closeBannerBtn && mobileAppBanner) {
    closeBannerBtn.addEventListener('click', () => {
      mobileAppBanner.classList.add('opacity-0', 'translate-y-full');
      localStorage.setItem('dccp-mobile-banner-dismissed', 'true');
      setTimeout(() => {
        mobileAppBanner.style.display = 'none';
      }, 500);
    });
  }

  // Auto-hide banner after 15 seconds
  if (!bannerDismissed && mobileAppBanner) {
    setTimeout(() => {
      if (mobileAppBanner && !mobileAppBanner.classList.contains('opacity-0')) {
        mobileAppBanner.classList.add('opacity-0', 'translate-y-full');
        setTimeout(() => {
          mobileAppBanner.style.display = 'none';
        }, 500);
      }
    }, 18000);
  }
</script>
