---
import { Badge } from "@/components/ui/badge";
---

<section class="py-20 bg-muted/30">
  <div class="container">
    <!-- Section header -->
    <div class="text-center max-w-3xl mx-auto">
      <Badge variant="outline" className="mb-4">Features</Badge>
      <h2 class="text-3xl md:text-4xl font-bold tracking-tight mb-4">Everything You Need in One Place</h2>
      <p class="text-muted-foreground text-lg mb-16">
        DCCPHub provides a comprehensive suite of tools designed specifically for the academic needs of both students and teachers.
      </p>
    </div>
    
    <!-- Features grid -->
    <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
      <!-- Feature 1: Course Management -->
      <div class="feature-card bg-background rounded-xl p-6 border shadow-sm transition-all duration-300 hover:shadow-md hover:border-primary/20">
        <div class="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-5">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Course Management</h3>
        <p class="text-muted-foreground mb-4">Access course materials, syllabi, and assignments in one organized location.</p>
        <ul class="space-y-2 text-sm">
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Digital course materials</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Assignment submission</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Resource libraries</span>
          </li>
        </ul>
      </div>
      
      <!-- Feature 2: Grade Tracking -->
      <div class="feature-card bg-background rounded-xl p-6 border shadow-sm transition-all duration-300 hover:shadow-md hover:border-primary/20">
        <div class="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-5">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M22 12h-4l-3 9L9 3l-3 9H2"></path></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Grade Tracking</h3>
        <p class="text-muted-foreground mb-4">Monitor academic performance with real-time grade updates and feedback.</p>
        <ul class="space-y-2 text-sm">
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Real-time grade updates</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Performance analytics</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Instructor feedback</span>
          </li>
        </ul>
      </div>
      
      <!-- Feature 3: Schedule Management -->
      <div class="feature-card bg-background rounded-xl p-6 border shadow-sm transition-all duration-300 hover:shadow-md hover:border-primary/20">
        <div class="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-5">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Schedule Management</h3>
        <p class="text-muted-foreground mb-4">Stay organized with class schedules, academic calendars, and event reminders.</p>
        <ul class="space-y-2 text-sm">
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Class timetables</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Deadline notifications</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Campus event calendar</span>
          </li>
        </ul>
      </div>
      
      <!-- Feature 4: Communication Tools -->
      <div class="feature-card bg-background rounded-xl p-6 border shadow-sm transition-all duration-300 hover:shadow-md hover:border-primary/20">
        <div class="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-5">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Communication Tools</h3>
        <p class="text-muted-foreground mb-4">Connect with instructors and peers through integrated messaging and forums.</p>
        <ul class="space-y-2 text-sm">
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Direct messaging</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Discussion forums</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Announcement broadcasts</span>
          </li>
        </ul>
      </div>
      
      <!-- Feature 5: Resource Library -->
      <div class="feature-card bg-background rounded-xl p-6 border shadow-sm transition-all duration-300 hover:shadow-md hover:border-primary/20">
        <div class="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-5">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"></path><path d="M18 14h-8"></path><path d="M15 18h-5"></path><path d="M10 6h8v4h-8V6Z"></path></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Resource Library</h3>
        <p class="text-muted-foreground mb-4">Access a comprehensive collection of digital learning materials and references.</p>
        <ul class="space-y-2 text-sm">
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>E-books and journals</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Video tutorials</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Research databases</span>
          </li>
        </ul>
      </div>
      
      <!-- Feature 6: Mobile Access -->
      <div class="feature-card bg-background rounded-xl p-6 border shadow-sm transition-all duration-300 hover:shadow-md hover:border-primary/20">
        <div class="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-5">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"></rect><path d="M12 18h.01"></path></svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">Mobile Access</h3>
        <p class="text-muted-foreground mb-4">Stay connected on the go with full mobile compatibility and dedicated apps.</p>
        <ul class="space-y-2 text-sm">
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Android app (Beta available)</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
            <span>iOS app (In development)</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Push notifications</span>
          </li>
          <li class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><polyline points="20 6 9 17 4 12"></polyline></svg>
            <span>Offline access to materials</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const featureCards = document.querySelectorAll('.feature-card');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            entry.target.classList.add('feature-visible');
          }, index * 100); // Staggered animation
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });
    
    featureCards.forEach(card => {
      observer.observe(card);
    });
  });
</script>

<style>
  .feature-card {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  }
  
  .feature-visible {
    opacity: 1;
    transform: translateY(0);
  }
</style>
