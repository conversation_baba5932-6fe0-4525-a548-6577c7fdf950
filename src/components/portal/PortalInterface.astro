---
import { Badge } from "@/components/ui/badge";
---

<section class="py-20 relative overflow-hidden">
  <!-- Background elements -->
  <div class="absolute inset-0 -z-10">
    <div class="absolute inset-0 bg-background"></div>
    <!-- Grid pattern background -->
    <div class="absolute inset-0 opacity-[0.02]" style="background-size: 40px 40px; background-image: linear-gradient(to right, var(--border) 1px, transparent 1px), linear-gradient(to bottom, var(--border) 1px, transparent 1px);"></div>
  </div>

  <div class="container">
    <!-- Section header -->
    <div class="text-center max-w-3xl mx-auto mb-16">
      <Badge variant="outline" className="mb-4">Interface</Badge>
      <h2 class="text-3xl md:text-4xl font-bold tracking-tight mb-4">Intuitive Design for Everyone</h2>
      <p class="text-muted-foreground text-lg">
        DCCPHub features a clean, modern interface that's easy to navigate for both students and teachers.
      </p>
    </div>

    <!-- Custom Tabs for different user interfaces -->
    <div class="interface-tabs w-full">
      <!-- Tab buttons -->
      <div class="flex justify-center mb-8">
        <div class="w-full max-w-md grid grid-cols-2 bg-muted rounded-lg p-1">
          <button
            id="student-tab"
            class="tab-button active py-3 px-4 rounded-md text-sm md:text-base font-medium transition-all duration-300"
            data-tab="student"
          >
            Student View
          </button>
          <button
            id="teacher-tab"
            class="tab-button py-3 px-4 rounded-md text-sm md:text-base font-medium transition-all duration-300"
            data-tab="teacher"
          >
            Teacher View
          </button>
        </div>
      </div>

      <!-- Tab content -->
      <div class="tab-content-container">
        <!-- Student Interface -->
        <div id="student-content" class="tab-content active">
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <div class="order-2 md:order-1">
              <h3 class="text-2xl font-semibold mb-4">Student Dashboard</h3>
              <p class="text-muted-foreground mb-6">
                Students enjoy a personalized dashboard that provides quick access to courses, assignments, grades, and important announcements.
              </p>

              <ul class="space-y-4">
                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Course Overview</h4>
                    <p class="text-sm text-muted-foreground">Quick access to all enrolled courses with progress indicators</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Assignment Tracker</h4>
                    <p class="text-sm text-muted-foreground">Upcoming and past assignments with due dates and status</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Grade Visualization</h4>
                    <p class="text-sm text-muted-foreground">Visual charts and graphs showing academic performance</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Notification Center</h4>
                    <p class="text-sm text-muted-foreground">Important alerts for deadlines, grades, and announcements</p>
                  </div>
                </li>
              </ul>
            </div>

            <div class="order-1 md:order-2 interface-image">
              <div class="relative mx-auto max-w-[500px]">
                <!-- Browser window mockup -->
                <div class="rounded-xl overflow-hidden shadow-lg border border-border/50 bg-card">
                  <!-- Browser content -->
                  <img
                    src="/images/portal/student-dashboard.png"
                    alt="Student Dashboard Interface"
                    class="w-full h-auto"
                  />
                </div>

                <!-- Decorative elements -->
                <div class="absolute -bottom-4 -right-4 h-20 w-20 rounded-full bg-primary/10 blur-xl"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Teacher Interface -->
        <div id="teacher-content" class="tab-content hidden">
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <div class="order-2 md:order-1">
              <h3 class="text-2xl font-semibold mb-4">Teacher Dashboard</h3>
              <p class="text-muted-foreground mb-6">
                Teachers have powerful tools to manage courses, track student progress, grade assignments, and communicate effectively.
              </p>

              <ul class="space-y-4">
                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Course Management</h4>
                    <p class="text-sm text-muted-foreground">Create and organize course content, materials, and assignments</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Grading Tools</h4>
                    <p class="text-sm text-muted-foreground">Efficient grading interface with rubrics and feedback options</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Student Analytics</h4>
                    <p class="text-sm text-muted-foreground">Detailed insights into student performance and engagement</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Communication Center</h4>
                    <p class="text-sm text-muted-foreground">Tools for announcements, messaging, and discussion moderation</p>
                  </div>
                </li>
              </ul>
            </div>

            <div class="order-1 md:order-2 interface-image">
              <div class="relative mx-auto max-w-[500px]">
                <!-- Browser window mockup -->
                <div class="rounded-xl overflow-hidden shadow-lg border border-border/50 bg-card">
                  <!-- Browser content -->
                  <img
                    src="/images/portal/teacher-dashboard.png"
                    alt="Teacher Dashboard Interface"
                    class="w-full h-auto"
                  />
                </div>

                <!-- Decorative elements -->
                <div class="absolute -bottom-4 -left-4 h-20 w-20 rounded-full bg-primary/10 blur-xl"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Apps Section -->
    <div class="mt-24 relative overflow-hidden">
      <!-- Background Elements -->
      <div class="absolute inset-0 -z-10">
        <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5"></div>
        <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur-3xl opacity-30 animate-pulse"></div>
      </div>

      <div class="container relative z-10">
        <!-- Section Header -->
        <div class="text-center mb-16">
          <Badge variant="outline" className="mb-4">Mobile Apps</Badge>
          <h3 class="text-3xl md:text-4xl font-bold tracking-tight mb-4">
            DCCPHub on the Go
          </h3>
          <p class="text-muted-foreground text-lg max-w-2xl mx-auto">
            Access your academic portal anywhere, anytime. Download our mobile app for a seamless learning experience.
          </p>
        </div>

        <!-- Mobile Apps Grid -->
        <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <!-- Android App Card -->
          <div class="group relative">
            <div class="relative bg-card border border-border/50 rounded-2xl p-8 transition-all duration-300 hover:shadow-xl hover:border-primary/20 hover:-translate-y-1">
              <!-- Status Badge -->
              <div class="absolute -top-3 left-6">
                <span class="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-green-500 text-white text-sm font-medium shadow-lg">
                  <div class="w-2 h-2 rounded-full bg-white animate-pulse"></div>
                  Beta Available
                </span>
              </div>

              <!-- Android Icon -->
              <div class="flex items-center justify-center w-16 h-16 rounded-2xl bg-green-500/10 mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="currentColor" class="text-green-500">
                  <path d="M17.523 15.3414c-.5665 0-1.0253-.4588-1.0253-1.0253s.4588-1.0253 1.0253-1.0253 1.0253.4588 1.0253 1.0253-.4588 1.0253-1.0253 1.0253zm-11.046 0c-.5665 0-1.0253-.4588-1.0253-1.0253s.4588-1.0253 1.0253-1.0253 1.0253.4588 1.0253 1.0253-.4588 1.0253-1.0253 1.0253zm11.405-6.02l1.14-2.02c.08-.14.03-.32-.11-.4-.14-.08-.32-.03-.4.11l-1.15 2.04c-.93-.41-1.96-.64-3.06-.64s-2.13.23-3.06.64L9.13 6.97c-.08-.14-.26-.19-.4-.11-.14.08-.19.26-.11.4l1.14 2.02C6.59 10.85 4.5 13.6 4.5 16.5h15c0-2.9-2.09-5.65-5.27-7.22z"/>
                </svg>
              </div>

              <div class="space-y-4">
                <div>
                  <h4 class="text-xl font-semibold mb-2">Android App</h4>
                  <p class="text-muted-foreground text-sm">
                    Download the beta version of DCCPHub for Android devices. Experience all portal features optimized for mobile.
                  </p>
                </div>

                <!-- Features List -->
                <ul class="space-y-2 text-sm">
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    <span>Full portal access</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    <span>Push notifications</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    <span>Offline grade viewing</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-500"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    <span>Quick assignment submission</span>
                  </li>
                </ul>

                <!-- Download Button -->
                <a
                  href="https://portal.dccp.edu.ph/apk/download/DCCPHub_latest.apk"
                  class="inline-flex items-center justify-center gap-2 w-full py-3 px-4 rounded-lg bg-green-500 text-white font-medium hover:bg-green-600 transition-colors duration-300 group/btn"
                  download
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="group-hover/btn:animate-bounce">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" x2="12" y1="15" y2="3"></line>
                  </svg>
                  Download Beta APK
                </a>

                <!-- Version Info -->
                <div class="text-xs text-muted-foreground text-center">
                  Beta Version • Android 6.0+
                </div>
              </div>
            </div>
          </div>

          <!-- iOS App Card -->
          <div class="group relative">
            <div class="relative bg-card border border-border/50 rounded-2xl p-8 transition-all duration-300 hover:shadow-xl hover:border-primary/20 hover:-translate-y-1 opacity-75">
              <!-- Status Badge -->
              <div class="absolute -top-3 left-6">
                <span class="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-blue-500 text-white text-sm font-medium shadow-lg">
                  <div class="w-2 h-2 rounded-full bg-white animate-pulse"></div>
                  In Development
                </span>
              </div>

              <!-- iOS Icon -->
              <div class="flex items-center justify-center w-16 h-16 rounded-2xl bg-blue-500/10 mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="currentColor" class="text-blue-500">
                  <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                </svg>
              </div>

              <div class="space-y-4">
                <div>
                  <h4 class="text-xl font-semibold mb-2">iOS App</h4>
                  <p class="text-muted-foreground text-sm">
                    We're working hard to bring DCCPHub to iOS devices. Stay tuned for updates on our development progress.
                  </p>
                </div>

                <!-- Planned Features List -->
                <ul class="space-y-2 text-sm">
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                    <span>Native iOS experience</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                    <span>Apple Watch integration</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                    <span>Siri shortcuts</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                    <span>Widget support</span>
                  </li>
                </ul>

                <!-- Coming Soon Button -->
                <div class="inline-flex items-center justify-center gap-2 w-full py-3 px-4 rounded-lg bg-muted text-muted-foreground font-medium cursor-not-allowed">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                  Coming Soon
                </div>

                <!-- Development Info -->
                <div class="text-xs text-muted-foreground text-center">
                  In Development • iOS 14.0+
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Info Section -->
        <div class="mt-16 text-center">
          <div class="max-w-2xl mx-auto">
            <h4 class="text-lg font-semibold mb-4">Why Choose DCCPHub Mobile?</h4>
            <div class="grid sm:grid-cols-3 gap-6 text-sm">
              <div class="space-y-2">
                <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                  </svg>
                </div>
                <h5 class="font-medium">Secure & Safe</h5>
                <p class="text-muted-foreground">End-to-end encryption and secure authentication</p>
              </div>
              <div class="space-y-2">
                <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                  </svg>
                </div>
                <h5 class="font-medium">Easy to Use</h5>
                <p class="text-muted-foreground">Intuitive design that students love</p>
              </div>
              <div class="space-y-2">
                <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                    <path d="M8 2v4"></path>
                    <path d="M16 2v4"></path>
                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                    <path d="M3 10h18"></path>
                  </svg>
                </div>
                <h5 class="font-medium">Always Updated</h5>
                <p class="text-muted-foreground">Real-time sync with your academic data</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Tab functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.getAttribute('data-tab');

        // Update active tab button
        tabButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        // Show corresponding content
        tabContents.forEach(content => {
          if (content.id === `${tabId}-content`) {
            content.classList.remove('hidden');
            content.classList.add('active');
          } else {
            content.classList.add('hidden');
            content.classList.remove('active');
          }
        });
      });
    });

    // Animation for images
    const interfaceImages = document.querySelectorAll('.interface-image');
    const mobileShowcase = document.querySelector('.mobile-showcase');

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });

    interfaceImages.forEach(image => {
      observer.observe(image);
    });

    if (mobileShowcase) {
      observer.observe(mobileShowcase);
    }
  });
</script>

<style>
  /* Tab styles */
  .tab-button {
    background-color: transparent;
    color: var(--muted-foreground);
  }

  .tab-button.active {
    background-color: var(--background);
    color: var(--foreground);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Tab content animation */
  .tab-content {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.5s ease, transform 0.5s ease;
  }

  .tab-content.active {
    opacity: 1;
    transform: translateY(0);
  }

  /* Image animations */
  .interface-image, .mobile-showcase {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }

  .animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes gradient-move {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.05); }
  }
  .animate-gradient-move {
    animation: gradient-move 8s ease-in-out infinite;
  }
  @keyframes gradient-shimmer {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
  }
  .animate-gradient-shimmer {
    background-size: 200% 200%;
    animation: gradient-shimmer 3s linear infinite;
  }
  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 0 0 rgba(59,130,246,0.3); }
    50% { box-shadow: 0 0 16px 4px rgba(59,130,246,0.5); }
  }
  .animate-pulse-glow {
    animation: pulse-glow 2s infinite;
  }
  @keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fade-in {
    animation: fade-in 1.2s cubic-bezier(0.16, 1, 0.3, 1) both;
  }
  @keyframes sparkle {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.3); }
  }
  .animate-sparkle {
    animation: sparkle 2.2s infinite;
  }
  .animate-sparkle-delay {
    animation: sparkle 2.2s 0.7s infinite;
  }
  .animate-sparkle-delay2 {
    animation: sparkle 2.2s 1.2s infinite;
  }

  /* Mobile app card hover effects */
  .group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
  }

  /* Download button animation */
  .group\/btn:hover .group-hover\/btn\:animate-bounce {
    animation: bounce 1s infinite;
  }

  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translateY(0);
    }
    40%, 43% {
      transform: translateY(-8px);
    }
    70% {
      transform: translateY(-4px);
    }
  }

  /* Mobile responsiveness for app cards */
  @media (max-width: 768px) {
    .grid.md\:grid-cols-2 {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .rounded-2xl {
      border-radius: 1rem;
    }

    .p-8 {
      padding: 1.5rem;
    }
  }
</style>
